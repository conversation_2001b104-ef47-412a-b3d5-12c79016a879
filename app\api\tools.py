from fastapi import APIRouter, Depends
from typing import List, Dict, Any
from sqlalchemy.orm import Session

from app.models.base import get_db
from app.agent.tools.base import tool_registry
from app.schemas.tool import ToolInfo, ToolListResponse

router = APIRouter()


@router.get("/", response_model=ToolListResponse)
async def list_tools():
    """
    List all available MCP tools.
    
    Returns:
        ToolListResponse: List of available tools with their schemas
    """
    # Import all tool modules to ensure they're registered
    from app.agent.tools import math_tools
    
    tool_names = tool_registry.list_tools()
    tools_info = []
    
    for tool_name in tool_names:
        tool = tool_registry.get_tool(tool_name)
        if tool:
            schema = tool.get_schema()
            tool_info = ToolInfo(
                name=schema.name,
                description=schema.description,
                parameters=schema.parameters,
                required=schema.required,
                category=_get_tool_category(tool_name)
            )
            tools_info.append(tool_info)
    
    return ToolListResponse(tools=tools_info, total=len(tools_info))


@router.get("/{tool_name}", response_model=ToolInfo)
async def get_tool_info(tool_name: str):
    """
    Get detailed information about a specific tool.
    
    Args:
        tool_name: Name of the tool
        
    Returns:
        ToolInfo: Tool information
        
    Raises:
        HTTPException: If tool not found
    """
    from fastapi import HTTPException, status
    
    # Import all tool modules to ensure they're registered
    from app.agent.tools import math_tools
    
    tool = tool_registry.get_tool(tool_name)
    if not tool:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tool '{tool_name}' not found"
        )
    
    schema = tool.get_schema()
    return ToolInfo(
        name=schema.name,
        description=schema.description,
        parameters=schema.parameters,
        required=schema.required,
        category=_get_tool_category(tool_name)
    )


def _get_tool_category(tool_name: str) -> str:
    """
    Determine the category of a tool based on its name.
    
    Args:
        tool_name: Name of the tool
        
    Returns:
        str: Tool category
    """
    if "math" in tool_name.lower() or "calculator" in tool_name.lower():
        return "mathematics"
    elif "web" in tool_name.lower() or "search" in tool_name.lower():
        return "web"
    elif "file" in tool_name.lower() or "document" in tool_name.lower():
        return "file_management"
    elif "code" in tool_name.lower() or "programming" in tool_name.lower():
        return "programming"
    else:
        return "general"
